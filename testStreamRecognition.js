const { recognizeImagesStream, recognizeImages } = require('./imageRecognize');
const { sampleBase64 } = require('./imageBase64');

// 测试流式图片识别
async function testStreamRecognition() {
  const imageUrls = [
    sampleBase64, // 图片的base64编码字符串
  ];
  
  const question = `
    你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
    ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
  `;

  console.log('=== 开始流式图片识别测试 ===\n');
  
  try {
    let receivedChunks = 0;
    
    const result = await recognizeImagesStream(
      imageUrls,
      question,
      // onData 回调 - 实时接收数据
      (chunk, fullContent) => {
        receivedChunks++;
        console.log(`[数据块 ${receivedChunks}]: "${chunk}"`);
        console.log(`[当前完整内容]: "${fullContent}"`);
        console.log('---');
      },
      // onComplete 回调 - 完成时调用
      (fullContent) => {
        console.log('\n🎉 流式识别完成！');
        console.log(`📝 完整结果: "${fullContent}"`);
        console.log(`📊 总共接收了 ${receivedChunks} 个数据块`);
      },
      // onError 回调 - 出错时调用
      (error) => {
        console.error('❌ 流式识别出错:', error.message);
      }
    );
    
    console.log('\n✅ 函数返回的最终结果:', result);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 对比测试：非流式 vs 流式
async function compareStreamVsNonStream() {
  const imageUrls = [sampleBase64];
  const question = `识别这张图片中的内容，简短回答。`;

  console.log('\n=== 对比测试：非流式 vs 流式 ===\n');

  // 测试非流式
  console.log('🔄 开始非流式识别...');
  const startTime1 = Date.now();
  
  try {
    const nonStreamResult = await recognizeImages(imageUrls, question);
    const endTime1 = Date.now();
    console.log(`⏱️  非流式耗时: ${endTime1 - startTime1}ms`);
    console.log(`📝 非流式结果: "${nonStreamResult}"`);
  } catch (error) {
    console.error('❌ 非流式识别失败:', error.message);
  }

  console.log('\n---\n');

  // 测试流式
  console.log('🔄 开始流式识别...');
  const startTime2 = Date.now();
  let firstChunkTime = null;
  
  try {
    const streamResult = await recognizeImagesStream(
      imageUrls,
      question,
      (chunk, fullContent) => {
        if (firstChunkTime === null) {
          firstChunkTime = Date.now();
          console.log(`⚡ 首个数据块到达时间: ${firstChunkTime - startTime2}ms`);
        }
        process.stdout.write(chunk); // 实时输出
      },
      (fullContent) => {
        const endTime2 = Date.now();
        console.log(`\n⏱️  流式总耗时: ${endTime2 - startTime2}ms`);
        console.log(`📝 流式完整结果: "${fullContent}"`);
      }
    );
  } catch (error) {
    console.error('❌ 流式识别失败:', error.message);
  }
}

// 运行测试
async function runTests() {
  await testStreamRecognition();
  await compareStreamVsNonStream();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testStreamRecognition,
  compareStreamVsNonStream,
  runTests
};
