const https = require('https')
const { v4: uuidv4 } = require('uuid')
const fs = require('fs')
// const base64 = require('base64-js');
const { PassThrough } = require('stream')

function bufferToPassThrough(buffer) {
  const passThrough = new PassThrough()
  passThrough.end(buffer) // 将 Buffer 写入流并结束
  return passThrough
}

async function saveAudioToFile(audioBuffer, outputPath) {
  return new Promise((resolve, reject) => {
    const audioStream = bufferToPassThrough(audioBuffer)
    const writeStream = fs.createWriteStream(outputPath)

    audioStream.pipe(writeStream)

    writeStream.on('finish', () => {
      console.log(`Audio saved to ${outputPath}`)
      resolve()
    })

    writeStream.on('error', (err) => {
      console.error(`Failed to save audio: ${err.message}`)
      reject(err)
    })
  })
}

const BearerToken = 'Zol8wcj36NJGQC141tLZgNkvdVg-o_a7' // Replace with your actual Bearer Token
const appID = '8359578722' // Replace with your actual app ID
const clusterName = 'volcano_tts' // Replace with your actual cluster name

function httpPost(url, headers, body, timeout) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url)
    const options = {
      hostname: parsedUrl.hostname,
      path: parsedUrl.pathname,
      method: 'POST',
      headers,
      timeout,
    }

    const req = https.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => resolve(data))
    })

    req.on('error', reject)
    req.write(body)
    req.end()
  })
}

async function synthesis(
  text,
  isBase64 = false,
  voiceType = 'BV700_streaming'
) {
  const reqID = uuidv4()
  const params = {
    app: {
      appid: appID,
      token: 'access_token',
      cluster: clusterName,
    },
    user: {
      uid: 'uid',
    },
    audio: {
      voice_type: voiceType,
      encoding: 'mp3',
      speed_ratio: 1.0,
      volume_ratio: 1.0,
      pitch_ratio: 1.0,
    },
    request: {
      reqid: reqID,
      text: text,
      text_type: 'plain',
      operation: 'query',
    },
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer;${BearerToken}`,
  }

  const url = 'https://openspeech.bytedance.com/api/v1/tts'
  const timeout = 30000 // 30 seconds

  return new Promise(async (resolve, reject) => {
    try {
      const body = JSON.stringify(params)
      const synResp = await httpPost(url, headers, body, timeout)

      const respJSON = JSON.parse(synResp)
      if (respJSON.code !== 3000) {
        console.error(`Code fail [code:${respJSON.code}]`)
        throw new Error('Response code failed')
      }
      if (isBase64) {
        resolve(respJSON.data)
      } else {
        const audio = Buffer.from(respJSON.data, 'base64')
        resolve(audio)
      }
    } catch (err) {
      console.error(`HTTP post failed [err:${err.message}]`)
      throw err
    }
  })
}

module.exports = { synthesis }

// let vList = ['BV034_streaming','BV705_streaming','BV033_streaming','BV007_streaming','BV115_streaming','BV113_streaming','BV701_streaming','BV700_streaming','BV005_streaming','BV102_streaming','BV119_streaming','BV051_streaming','BV056_streaming']

// vList.forEach(i => {
//   synthesis('你好 我是小蓝, 我是你的贴心助手.',false,i).then((audioBuffer) => {
//     const outputPath = i + '.mp3'
//     saveAudioToFile(audioBuffer, outputPath)
//   })
// })

let voic = 'BV051_streaming'
let te = '你好，我是奶气萌娃'
console.log(new Date().toISOString(),'start')
// synthesis(
//   te,
//   false,
//   voic
// ).then((audioBuffer) => {
//   const outputPath = te + '.mp3'
//   console.log(new Date().toISOString(),'end')
//   saveAudioToFile(audioBuffer, outputPath)

// })
