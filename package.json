{"name": "voice-cloning-demo", "version": "1.0.0", "description": "火山引擎声音复刻API Demo", "main": "voiceCopy.js", "scripts": {"start": "node tts_server.js", "dev": "node voiceCopy.js", "server": "node server.js"}, "dependencies": {"@tuya/tuya-connector-nodejs": "^2.1.2", "axios": "^1.10.0", "body-parser": "^1.20.3", "crypto": "^1.0.1", "express": "^4.21.1", "fs": "0.0.1-security", "openai": "^4.68.4", "path": "^0.12.7", "uuid": "^11.0.1", "ws": "^8.18.0", "zlib": "^1.0.5"}, "author": "", "license": "ISC"}