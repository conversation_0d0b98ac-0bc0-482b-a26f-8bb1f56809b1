const { chatDoubao } = require('./chat_doubao');
const { sampleBase64 } = require('./imageBase64');

// 示例1: 纯文本聊天（原有功能）
function exampleTextChat() {
  console.log('=== 示例1: 纯文本聊天 ===');
  
  chatDoubao(
    '你好，请用一句话介绍人工智能',
    (response) => {
      if (response.target_text) {
        process.stdout.write(response.target_text);
      }
      
      if (response.finish_reason === 'STOP') {
        console.log('\n✅ 对话结束\n');
      }
    },
    false, // 非音频模式
    []     // 无图片
  );
}

// 示例2: 图片识别聊天（新功能）
function exampleImageChat() {
  console.log('=== 示例2: 图片识别聊天 ===');
  
  chatDoubao(
    '请简单描述这张图片的内容',
    (response) => {
      if (response.target_text) {
        process.stdout.write(response.target_text);
      }
      
      if (response.finish_reason === 'STOP') {
        console.log('\n✅ 图片识别完成\n');
      }
    },
    false,
    [sampleBase64] // 包含图片
  );
}

// 示例3: 多张图片分析（新功能）
function exampleMultiImageChat() {
  console.log('=== 示例3: 多张图片分析 ===');
  
  chatDoubao(
    '这些图片有什么共同特点？',
    (response) => {
      if (response.target_text) {
        process.stdout.write(response.target_text);
      }
      
      if (response.finish_reason === 'STOP') {
        console.log('\n✅ 多图片分析完成\n');
      }
    },
    false,
    [sampleBase64, sampleBase64] // 多张图片
  );
}

// 示例4: 实时显示效果
function exampleRealTimeDisplay() {
  console.log('=== 示例4: 实时显示效果 ===');
  console.log('🤖 AI正在回复: ');
  
  let fullText = '';
  
  chatDoubao(
    '请详细解释什么是机器学习',
    (response) => {
      if (response.target_text) {
        // 实时显示，模拟打字机效果
        process.stdout.write(response.target_text);
        fullText += response.target_text;
      }
      
      if (response.finish_reason === 'STOP') {
        console.log('\n');
        console.log(`📝 完整回复长度: ${fullText.length} 字符`);
        console.log('✅ 实时显示完成\n');
      }
    },
    false,
    []
  );
}

// 运行示例的函数
async function runExamples() {
  console.log('🚀 chat_doubao.js 新功能演示\n');
  console.log('📋 主要改进:');
  console.log('  ✅ 从 OpenAI SDK 改为 axios 流式实现');
  console.log('  ✅ 新增完整的图片输入支持');
  console.log('  ✅ 保持所有原有功能不变');
  console.log('  ✅ 增强错误处理和流式体验\n');
  
  // 依次运行示例（注释掉避免同时运行多个）
  exampleTextChat();
  
  // 等待一段时间后运行下一个示例
  setTimeout(() => {
    exampleImageChat();
  }, 5000);
  
  setTimeout(() => {
    exampleMultiImageChat();
  }, 10000);
  
  setTimeout(() => {
    exampleRealTimeDisplay();
  }, 15000);
}

// 单独运行某个示例的函数
function runSingleExample(exampleName) {
  switch(exampleName) {
    case 'text':
      exampleTextChat();
      break;
    case 'image':
      exampleImageChat();
      break;
    case 'multi':
      exampleMultiImageChat();
      break;
    case 'realtime':
      exampleRealTimeDisplay();
      break;
    default:
      console.log('可用示例: text, image, multi, realtime');
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const exampleType = process.argv[2];
  
  if (exampleType) {
    runSingleExample(exampleType);
  } else {
    console.log('使用方法:');
    console.log('  node example_usage.js text     # 纯文本聊天');
    console.log('  node example_usage.js image    # 图片识别');
    console.log('  node example_usage.js multi    # 多图片分析');
    console.log('  node example_usage.js realtime # 实时显示');
    console.log('  node example_usage.js          # 显示帮助');
    console.log('\n或者取消注释 runExamples() 来运行所有示例');
    // runExamples();
  }
}

module.exports = {
  exampleTextChat,
  exampleImageChat,
  exampleMultiImageChat,
  exampleRealTimeDisplay,
  runExamples,
  runSingleExample
};
