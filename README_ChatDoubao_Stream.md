# chat_doubao.js 流式改造说明

## 概述

`chat_doubao.js` 已成功从使用 OpenAI SDK 改造为使用原生 axios 的流式实现，并新增了图片输入支持。

## 主要改动

### 🔧 **技术架构变更**

1. **替换 OpenAI SDK**: 从 `openai` 包改为使用 `axios` 进行 HTTP 请求
2. **流式处理**: 使用 axios 的 `responseType: 'stream'` 实现流式数据接收
3. **图片支持**: 新增对 `imageUrls` 参数的完整支持

### 📋 **API 接口保持不变**

```javascript
async function chatDoubao(message, callback, isAudio, imageUrls)
```

- `message`: 文本消息（必需）
- `callback`: 响应回调函数（必需）
- `isAudio`: 是否音频模式（可选，默认 false）
- `imageUrls`: 图片URL数组（可选，默认 []）**现在完全支持**

### 🎯 **新增功能特性**

#### 1. 图片输入支持

```javascript
// 纯文本聊天（原有功能）
chatDoubao('你好', callback, false, []);

// 图片+文本聊天（新功能）
chatDoubao('描述这张图片', callback, false, [imageUrl]);

// 多张图片聊天（新功能）
chatDoubao('比较这些图片', callback, false, [url1, url2, url3]);
```

#### 2. 消息格式自动适配

- **纯文本**: 直接发送文本消息
- **包含图片**: 自动构建多模态消息格式

<augment_code_snippet path="chat_doubao.js" mode="EXCERPT">
```javascript
const buildUserContent = () => {
  const content = [{ text: message, type: "text" }];
  
  if (imageUrls && imageUrls.length > 0) {
    imageUrls.forEach(url => {
      content.push({
        image_url: { url: url },
        type: "image_url"
      });
    });
  }
  return content;
};
```
</augment_code_snippet>

#### 3. 流式数据处理

<augment_code_snippet path="chat_doubao.js" mode="EXCERPT">
```javascript
response.data.on('data', (chunk) => {
  const lines = chunk.toString().split('\n');
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(jsonStr);
      if (data.choices?.[0]?.delta?.content) {
        const content = data.choices[0].delta.content;
        processContent(content);
      }
    }
  }
});
```
</augment_code_snippet>

### 🚀 **使用示例**

#### 基础文本聊天

```javascript
const { chatDoubao } = require('./chat_doubao');

chatDoubao(
  '你好，请介绍一下自己',
  (response) => {
    console.log('AI回复:', response.target_text);
    if (response.finish_reason === 'STOP') {
      console.log('对话结束');
    }
  },
  false, // 非音频模式
  []     // 无图片
);
```

#### 图片识别聊天

```javascript
const { sampleBase64 } = require('./imageBase64');

chatDoubao(
  '请描述这张图片的内容',
  (response) => {
    if (response.target_text) {
      process.stdout.write(response.target_text); // 实时显示
    }
    if (response.finish_reason === 'STOP') {
      console.log('\n识别完成');
    }
  },
  false,
  [sampleBase64] // 包含图片
);
```

#### 多图片分析

```javascript
chatDoubao(
  '比较这些图片的异同点',
  (response) => {
    console.log(response.target_text);
  },
  false,
  [image1, image2, image3] // 多张图片
);
```

### 📊 **性能优势**

1. **更快的首字节时间**: 使用原生 axios 减少了 SDK 开销
2. **更好的错误控制**: 直接处理 HTTP 响应和错误
3. **更灵活的配置**: 可以直接控制请求参数和头部

### 🔄 **向后兼容性**

- ✅ **完全兼容**: 所有原有的纯文本聊天功能保持不变
- ✅ **参数兼容**: API 接口完全一致
- ✅ **回调格式**: 响应格式保持原有结构
- ✅ **TTS 集成**: 与 `tts_doubao.js` 的集成保持不变

### 🧪 **测试**

运行测试文件验证功能：

```bash
node testChatDoubaoStream.js
```

测试包含：
- 纯文本聊天测试
- 图片+文本聊天测试
- 多张图片聊天测试
- 实时显示效果测试
- 错误处理测试

### 📝 **注意事项**

1. **图片格式**: 支持 base64 编码和 HTTP URL 格式的图片
2. **网络稳定性**: 流式输出对网络连接稳定性要求较高
3. **错误处理**: 增强了网络错误和解析错误的处理
4. **资源管理**: 流式连接会自动管理，无需手动释放

### 🔧 **技术细节**

#### 请求格式变化

**之前 (OpenAI SDK)**:
```javascript
const stream = await client.chat.completions.create({
  model: 'ep-20250704102320-5tnrj',
  messages: updatedHistory,
  stream: true,
});
```

**现在 (axios)**:
```javascript
const response = await axios.post(
  'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  requestBody,
  {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_KEY}`
    },
    responseType: 'stream'
  }
);
```

#### 流处理变化

**之前**:
```javascript
for await (const chunk of stream) {
  const delta = chunk.choices[0]?.delta || {};
  const content = delta.content || '';
  // 处理内容...
}
```

**现在**:
```javascript
response.data.on('data', (chunk) => {
  // 解析 SSE 格式数据
  // 处理 JSON 数据
  // 调用内容处理函数
});
```

这次改造成功实现了：
- ✅ 从 OpenAI SDK 迁移到原生 axios
- ✅ 完整的图片输入支持
- ✅ 保持所有原有功能
- ✅ 增强的错误处理和流式体验
