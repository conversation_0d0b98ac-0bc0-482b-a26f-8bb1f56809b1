const axios = require('axios')
const { sampleBase64 } = require('./imageBase64')

const API_KEY = '3c4635c7-281b-4a28-951d-0b674d7ad600';

/**
 * 使用火山方舟API识别图片内容（流式输出）
 * @param {Array<string>} imageUrls - 图片URL数组
 * @param {string} question - 向模型提问的问题
 * @param {Function} onData - 流式数据回调函数，接收每个数据块
 * @param {Function} onComplete - 完成回调函数，接收完整的结果
 * @param {Function} onError - 错误回调函数
 * @returns {Promise<string>} - 完整的识别结果
 */
async function recognizeImagesStream(imageUrls, question = `
    你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
    ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
  `, onData = null, onComplete = null, onError = null) {

  return new Promise((resolve, reject) => {
    try {
      // 构建消息内容
      const content = [
        {
          text: question,
          type: "text"
        }
      ];

      // 添加所有图片URL到内容中
      imageUrls.forEach(url => {
        content.push({
          image_url: {
            url: url
          },
          type: "image_url"
        });
      });

      // 构建请求体
      const requestBody = {
        messages: [
          {
            content: content,
            role: "user"
          }
        ],
        model: "ep-20250704102320-5tnrj",
        stream: true  // 启用流式输出
      };

      // 发送流式请求到火山方舟API
      axios.post(
        'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${API_KEY}`
          },
          responseType: 'stream'  // 设置响应类型为流
        }
      ).then(response => {
        let fullContent = '';

        // 监听数据流
        response.data.on('data', (chunk) => {
          try {
            const lines = chunk.toString().split('\n');

            for (const line of lines) {
              if (line.trim() === '') continue;
              if (line.trim() === 'data: [DONE]') {
                // 流结束
                if (onComplete) onComplete(fullContent);
                resolve(fullContent);
                return;
              }

              if (line.startsWith('data: ')) {
                const jsonStr = line.slice(6); // 移除 "data: " 前缀
                try {
                  const data = JSON.parse(jsonStr);
                  if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                    const content = data.choices[0].delta.content;
                    fullContent += content;

                    // 调用数据回调函数
                    if (onData) onData(content, fullContent);
                  }
                } catch (parseError) {
                  // 忽略JSON解析错误，继续处理下一行
                  console.warn('JSON解析错误:', parseError.message);
                }
              }
            }
          } catch (chunkError) {
            console.error('处理数据块错误:', chunkError);
            if (onError) onError(chunkError);
          }
        });

        // 监听流结束
        response.data.on('end', () => {
          if (onComplete) onComplete(fullContent);
          resolve(fullContent);
        });

        // 监听错误
        response.data.on('error', (error) => {
          console.error('流数据错误:', error);
          if (onError) onError(error);
          reject(error);
        });

      }).catch(error => {
        console.error('图片识别失败:', error.response?.data || error.message);
        if (onError) onError(error);
        reject(error);
      });

    } catch (error) {
      console.error('请求构建失败:', error);
      if (onError) onError(error);
      reject(error);
    }
  });
}

/**
 * 使用火山方舟API识别图片内容（非流式，保持向后兼容）
 * @param {Array<string>} imageUrls - 图片URL数组
 * @param {string} question - 向模型提问的问题
 * @returns {Promise<string>} - API响应结果
 */
async function recognizeImages(imageUrls, question = `
    你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
    ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
  `) {
  try {
    // 构建消息内容
    const content = [
      {
        text: question,
        type: "text"
      }
    ];

    // 添加所有图片URL到内容中
    imageUrls.forEach(url => {
      content.push({
        image_url: {
          url: url
        },
        type: "image_url"
      });
    });

    // 构建请求体
    const requestBody = {
      messages: [
        {
          content: content,
          role: "user"
        }
      ],
      model: "ep-20250704102320-5tnrj",
      stream: false
    };

    // 发送请求到火山方舟API
    const response = await axios.post(
      'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`
        }
      }
    );

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('图片识别失败:', error.response?.data || error.message);
    throw error;
  }
}

// 使用示例 - 非流式
async function example() {
  const imageUrls = [
    sampleBase64, // 图片的base64编码字符串
  ];
  const question = `
    你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
    ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
  `

  try {
    const result = await recognizeImages(imageUrls, question);
    console.log('识别结果:', result);
  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// 使用示例 - 流式输出
async function exampleStream() {
  const imageUrls = [
    sampleBase64, // 图片的base64编码字符串
  ];
  const question = `
    你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
    ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
  `

  try {
    console.log('开始流式识别...');

    const result = await recognizeImagesStream(
      imageUrls,
      question,
      // onData 回调 - 每次接收到新的数据块时调用
      (chunk, fullContent) => {
        process.stdout.write(chunk); // 实时输出每个字符
      },
      // onComplete 回调 - 完成时调用
      (fullContent) => {
        console.log('\n\n流式识别完成！');
        console.log('完整结果:', fullContent);
      },
      // onError 回调 - 出错时调用
      (error) => {
        console.error('流式识别出错:', error);
      }
    );

    console.log('最终返回结果:', result);
  } catch (error) {
    console.error('流式示例运行失败:', error);
  }
}

// 取消注释以运行示例
// example();
// exampleStream();

// 导出函数供其他模块使用
module.exports = {
  recognizeImages,        // 非流式版本（向后兼容）
  recognizeImagesStream   // 流式版本
};
