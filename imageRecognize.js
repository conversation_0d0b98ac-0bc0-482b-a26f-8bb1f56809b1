const axios = require('axios')
const { sampleBase64 } = require('./imageBase64')

const API_KEY = '3c4635c7-281b-4a28-951d-0b674d7ad600';

/**
 * 使用火山方舟API识别图片内容
 * @param {Array<string>} imageUrls - 图片URL数组
 * @param {string} question - 向模型提问的问题
 * @returns {Promise<Object>} - API响应结果
 */
async function recognizeImages(imageUrls, question = `
    你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
    ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
  `) {
  try {
    // 构建消息内容
    const content = [
      {
        text: question,
        type: "text"
      }
    ];

    // 添加所有图片URL到内容中
    imageUrls.forEach(url => {
      content.push({
        image_url: {
          url: url
        },
        type: "image_url"
      });
    });

    // 构建请求体
    const requestBody = {
      messages: [
        {
          content: content,
          role: "user"
        }
      ],
      model: "ep-20250704102320-5tnrj",
      stream: false
    };

    // 发送请求到火山方舟API
    const response = await axios.post(
      'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`
        }
      }
    );

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('图片识别失败:', error.response?.data || error.message);
    throw error;
  }
}

// 使用示例
async function example() {
  const imageUrls = [
    sampleBase64, // 图片的base64编码字符串
  ];
  const question = `
    你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
    ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
  `
  
  try {
    const result = await recognizeImages(imageUrls, question);
    console.log('识别结果:', result);
  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// example();

// 导出函数供其他模块使用
module.exports = {
  recognizeImages
};
