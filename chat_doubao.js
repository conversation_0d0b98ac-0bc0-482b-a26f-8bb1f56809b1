const axios = require('axios')
const { synthesis } = require('./tts_doubao.js')

const API_KEY = '3c4635c7-281b-4a28-951d-0b674d7ad600';

async function chatDoubao(message, callback = (res) => {}, isAudio = false, imageUrls=[]) {
  console.log(new Date().toISOString(), `start Chat`, message)
  if (!message) message = '你好'

  let isStart = false

  let isSynthesising = false
  let synList = []
  let synCount = 0
  let chatCount = 0
  let isEnd = false

  // 构建消息内容，支持图片
  const buildUserContent = () => {
    const content = [
      {
        text: message,
        type: "text"
      }
    ];

    // 如果有图片URL，添加到内容中
    if (imageUrls && imageUrls.length > 0) {
      imageUrls.forEach(url => {
        content.push({
          image_url: {
            url: url
          },
          type: "image_url"
        });
      });
    }

    return content;
  };

  const updatedHistory = [
    {
      role: 'system',
      content: '用户输入的文本由语音识别而来，请尽量回复简洁的文本。最大长度150字。',
    },
    {
      role: 'user',
      content: imageUrls && imageUrls.length > 0 ? buildUserContent() : message
    },
  ]

  // 构建请求体
  const requestBody = {
    messages: updatedHistory,
    model: 'ep-20250704102320-5tnrj',
    stream: true
  };

  try {
    // 使用 axios 发送流式请求
    const response = await axios.post(
      'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`
        },
        responseType: 'stream'
      }
    );


    let buffer = ''
    let preText = ''

    let interval = setInterval(() => {
      if (!isSynthesising && synList.length > 0) {
        isSynthesising = true

        let t = synList.shift()
        t = t.replace(/\n|《|》|/g, '')
        
        console.log(new Date().toISOString(), `start tts`, t)
        synthesis(t,true).then((res) => {
          isSynthesising = false
          synCount++
          const rJson = {
            target_audio: '',
            source_text: message,
            target_text: t,
            target_audio_format: 'pcm',
            sample_rate: 22050,
            finish_reason: synCount > 0 ? '' : 'START',
            is_tuya: '0',
            tuya_ret: '0',
          }
          rJson.target_audio = res
          console.log(new Date().toISOString(), `end tts`, t)
          callback(rJson)
          if (synCount === chatCount && isEnd) {
            console.log('chat end')
            clearInterval(interval)
            const rJson = {
              target_audio: '',
              source_text: message,
              target_text: '',
              target_audio_format: 'pcm',
              sample_rate: 22050,
              finish_reason: 'STOP',
              is_tuya: '0',
              tuya_ret: '0',
            }
            callback(rJson)
          }
        })
      }
    }, 10)

    // 处理流式响应数据
    response.data.on('data', (chunk) => {
      try {
        const lines = chunk.toString().split('\n');

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (line.trim() === 'data: [DONE]') {
            // 流结束处理
            handleStreamEnd();
            return;
          }

          if (line.startsWith('data: ')) {
            const jsonStr = line.slice(6); // 移除 "data: " 前缀
            try {
              const data = JSON.parse(jsonStr);
              if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                const content = data.choices[0].delta.content;
                processContent(content);
              }
            } catch (parseError) {
              // 忽略JSON解析错误，继续处理下一行
              console.warn('JSON解析错误:', parseError.message);
            }
          }
        }
      } catch (chunkError) {
        console.error('处理数据块错误:', chunkError);
      }
    });

    // 流结束事件
    response.data.on('end', () => {
      handleStreamEnd();
    });

    // 流错误事件
    response.data.on('error', (error) => {
      console.error('流数据错误:', error);
      handleError(error);
    });

    // 处理内容的函数
    function processContent(content) {
      buffer += content

      // 增加对标点符号（包括引号内外）的处理，适配中文、英文等语言
      const isThai = /[\u0E00-\u0E7F]/.test(content)
      let punctuations = ''

      if (isThai) {
        // 泰语标点符号和引号的匹配
        punctuations = /!?.\s/ // 匹配空格（包括一个或多个空格）
      } else {
        // 中文、英文的标点符号和引号匹配
        punctuations = /([，,.!?。！？¿¡…~])/ // 匹配标点符号和引号
      }
      let segments = buffer.split(punctuations)
      buffer = segments.pop() // 保留最后不完整的部分

      // let inQuotes = false // 标记是否在引号内

      for (let i = 0; i < segments.length; i += 2) {
        const segment = segments[i]?.trim() // 避免 undefined 错误
        const punctuation = segments[i + 1] // 获取标点符号或引号

        // 确保 segment 和 punctuation 存在，避免 undefined 错误
        if (isThai) {
          // 泰语标点符号和引号的匹配
          if (!segment) continue
        } else {
          if (!segment || !punctuation) continue
        }

        // if (punctuation === '“' || punctuation === '”' || punctuation === '"') {
        //   // 处理引号的开闭
        //   inQuotes = !inQuotes
        //   buffer += segment + punctuation // 保留引号内的内容在 buffer 中
        //   continue
        // }

        if (segment && (punctuation || isThai)) {
          // 只有在引号外且有完整的标点时返回句子
          let completeSegment =
            (segment || '') + (punctuation ? punctuation : '')

          if (completeSegment.length < 8) {
            preText += completeSegment
          } else {
            completeSegment = preText + completeSegment
            preText = ''

            const rJson = {
              target_audio: '',
              source_text: message,
              target_text: completeSegment,
              target_audio_format: 'pcm',
              sample_rate: 22050,
              finish_reason: isStart ? '' : 'START',
              is_tuya: '0',
              tuya_ret: '0',
            }

            if (!isStart) {
              
              isStart = true
    
            }
            if (completeSegment) {
              chatCount++
              synList.push(completeSegment)
            }
          }
        } else {
          // 如果在引号内或没有完整句子，保留到 buffer 中
          buffer += segment + punctuation
        }
      }
    }

    // 流结束处理函数
    function handleStreamEnd() {
      if (preText) {
        const rJson = {
          target_audio: '',
          source_text: message,
          target_text: preText,
          target_audio_format: 'pcm',
          sample_rate: 22050,
          finish_reason: isStart ? '' : 'START',
          is_tuya: '0',
          tuya_ret: '0',
        }
        if (preText) {
          chatCount++
          synList.push(preText)
        }
      }
      if (!isStart) {
        const rJson = {
          target_audio: '',
          source_text: message,
          target_text: buffer.trim(),
          target_audio_format: 'pcm',
          sample_rate: 22050,
          finish_reason: 'START',
          is_tuya: '0',
          tuya_ret: '0',
        }

        if (buffer.trim()) {
          chatCount++
          synList.push(buffer.trim())
        }
      }
      const rJson = {
        target_audio: '',
        source_text: message,
        target_text: '',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'STOP',
        is_tuya: '0',
        tuya_ret: '0',
      }
      isEnd = true
    }

    // 错误处理函数
    function handleError(error) {
      const rJson = {
        target_audio: '',
        source_text: message,
        target_text: '',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'STOP',
        is_tuya: '0',
        tuya_ret: '0',
        status: 'error',
        message: error.message,
      }
      isEnd = true
      callback(rJson)
    }
  } catch (error) {
    console.error('请求失败:', error.response?.data || error.message);
    const rJson = {
      target_audio: '',
      source_text: message,
      target_text: '',
      target_audio_format: 'pcm',
      sample_rate: 22050,
      finish_reason: 'STOP',
      is_tuya: '0',
      tuya_ret: '0',
      status: 'error',
      message: error.message,
    }
    isEnd = true
    callback(rJson)
  }
}

module.exports = { chatDoubao }