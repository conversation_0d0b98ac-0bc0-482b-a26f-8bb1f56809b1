const { AsrClient } = require('./asrClient') // Import the AsrClient class
const PORT = 9444
const WebSocket = require('ws')
const { chatDoubao } = require('./chat_doubao')

const { preCheckCall } = require('./preCheckcall')
const { textToImage } = require('./textToImage')
const { getCall } = require('./callPerson')
const express = require('express')
const bodyParser = require('body-parser')
const http = require('http')
const { textToSound, getSongStatus } = require('./textToSound')
const { getSmartControl } = require('./smartControl_doubao')
const { generateToken } = require('./accessTokenTest')
const { startVoiceChat } = require('./startVoiceCall')
const { stopVoiceCall } = require('./stopVoiceCall')
const { updateVoiceChat } = require('./updateVoiceChat')
const {
  getDeviceListDetail,
  getAllSceneList,
  triggerScene,
  controlDevice,
} = require('./tuya')
const { checkDeviceType } = require('./checkDeviceType')
const { translateDoubao } = require('./translate_doubao')
const { recognizeImages } = require('./imageRecognize')

let tuyaDeviceMap = {}

let isGettingList = false

// 使用 body-parser 中间件解析 JSON 和 URL 编码的请求体
const app = express()
// 增加请求体的大小限制，这应该在应用的最顶层、路由之前设置
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// 您的 CORS 设置可以保留在这里
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*'); // 允许所有来源
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS'); // 允许的请求方法
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // 允许的自定义头
  next();
});

let songList = []
let tuyaIdList = [
  '6c94ec5bac975489ecvfh1',
  '6c2868e61e08720c41xrf2',
  '6c02e4178dd9e0f45c6pxb',
]
let isGeneratedSong = false

app.post('/gen_song', async (req, res) => {
  console.log(req.body.SongDetail)
  isGeneratedSong && songList.push(req.body.SongDetail)
  res.json({
    code: 0,
    Code: 0,
    message: 'success',
    Message: 'success',
  })
})

app.post('/gen_token', async (req, res) => {
  const roomID = req.body.room_id
  const userID = req.body.user_id

  if (userID?.length > 16) {
    if (tuyaIdList.includes(userID)) {
    } else {
      tuyaIdList.push(userID)
    }
  }

  console.log(
    new Date().toISOString().replace(/T |Z/g, ' '),
    'gen_token',
    JSON.stringify(req.body)
  )

  if (!roomID)
    return res.json({
      ret: -1,
      message: 'room_id is required',
    })

  if (!userID)
    return res.json({
      ret: -1,
      message: 'user_id is required',
    })

  const token = generateToken({
    roomID,
    userID,
  })
  res.json({
    ret: 0,
    message: 'ok',
    token,
  })
})

app.post('/start_voice_chat', async (req, res) => {
  const roomID = req.body.room_id
  const userID = req.body.user_id
  const time = req.body?.time
  let lang = req.body?.lang
  const addressBook = req.body?.address_book
  const isTuya = req.body?.is_tuya === 1 || req.body?.is_tuya == '1'
  const isTranslate = req.body?.is_translate == '1'
  let voiceType = req.body?.voice_type
  let systemPrompt = req.body?.system_prompt
  let characterType = req.body?.character_type
  console.log(
    new Date().toISOString().replace(/T|Z/g, ' '),
    'start_voice_chat',
    JSON.stringify(req.body)
  )

  if (!voiceType) voiceType = 'BV005_streaming'

  if (!roomID)
    return res.json({
      ret: -1,
      message: 'room_id is required',
    })

  if (!userID)
    return res.json({
      ret: -1,
      message: 'user_id is required',
    })

  let timeout = Math.round(time) * 1000

  if (isTuya) {
    if (!lang) {
      lang = 'id'
    } else {
      if (lang === 'en') lang = 'id'
      // else if (lang === 'id') lang = 'sp'
    }
    if (tuyaIdList.includes(userID)) {
    } else {
      tuyaIdList.push(userID)
    }
  }

  if (!lang) {
    lang = 'zh'
  }

  console.log('characterType', characterType)

  setTimeout(
    () => {
      const tuyaDevice = tuyaDeviceMap[userID]
        ? tuyaDeviceMap[userID]
        : { deviceList: [], sceneList: [], commandList: [] }
      startVoiceChat({
        RoomId: roomID,
        UserId: userID,
        isTuya,
        tuyaDevice,
        lang,
        voiceType,
        isTranslate,
        systemPrompt,
        addressBook,
        characterType,
      })
        .then((r) => {
          res.json({
            code: 0,
            message: r.Result,
            data: r.ResponseMetadata,
          })
        })
        .catch((err) => {
          console.log(err)
          res.json({
            code: -100,
            message: '服务器错误',
          })
        })
    },
    timeout ? timeout : 0
  )
})

app.post('/stop_voice_chat', async (req, res) => {
  const roomID = req.body.room_id
  const userID = req.body.user_id
  const taskID = req.body.task_id

  if (!roomID)
    return res.json({
      ret: -1,
      message: 'room_id is required',
    })

  if (!userID)
    return res.json({
      ret: -1,
      message: 'user_id is required',
    })

  stopVoiceCall({
    RoomId: roomID,
    UserId: userID,
    TaskId: taskID,
  })
    .then((r) => {
      res.json({
        code: 0,
        message: r.Result,
      })
    })
    .catch((err) => {
      console.log(err)
      res.json({
        code: -100,
        message: '服务器错误',
      })
    })
})

app.post('/update_voice_chat', async (req, res) => {
  const roomID = req.body.room_id
  const userID = req.body.user_id
  const ToolCallID = req.body?.tool_call_id
  const Content = req.body?.content

  if (!roomID)
    return res.json({
      ret: -1,
      message: 'room_id is required',
    })

  if (!userID)
    return res.json({
      ret: -1,
      message: 'user_id is required',
    })

  console.log('updateVoiceChat', JSON.stringify(req.body))

  updateVoiceChat({
    RoomId: roomID,
    UserId: userID,
    Command: ToolCallID ? 'function' : 'interrupt',
    Message: ToolCallID
      ? {
        ToolCallID,
        Content,
      }
      : '',
  })
    .then((r) => {
      res.json({
        code: 0,
        message: r.Result,
      })
    })
    .catch((err) => {
      console.log(err)
      res.json({
        code: -100,
        message: '服务器错误',
      })
    })
})

app.post('/tuya_control', async (req, res) => {
  const matched_devices_ids = req.body?.matched_devices_ids
  const matched_scene_id = req.body?.matched_scene_id
  const command = req.body?.command
  const value = req.body?.value
  const type = req.body?.type
  const command_name = req.body?.command_name
  const command_type = req.body?.command_type

  console.log(
    new Date().toISOString().replace(/T|Z/g, ' '),
    'tuya_control',
    JSON.stringify(req.body)
  )
  if (type === 0 || type == '0') {
    // 控制设备
    controlDevice({
      idList: matched_devices_ids.split(','),
      type: command,
      value: Math.round(value),
      valType: command_type,
      typeName: command_name,
    }).then((_res) => {
      console.log('_res', _res)
      res.json(_res)
    })
  } else {
    // 控制场景
    triggerScene({
      id: matched_scene_id,
    }).then((_res) => {
      res.json({ ret: 0 })
    })
  }
})

app.post('/tuya_get_device_list', async (req, res) => {
  const user_id = req.body?.user_id
  console.log(
    new Date().toISOString().replace(/T|Z/g, ' '),
    'tuya_get_device_list',
    JSON.stringify(req.body)
  )
  if (!user_id) {
    return res.json({
      ret: -1,
      message: 'device_id is required',
    })
  }
  if (tuyaDeviceMap[user_id]) {
    const deviceList = tuyaDeviceMap[user_id].deviceList
    res.json({
      deviceList,
    })
  } else {
    res.json({
      ret: -2,
      message: 'current user_id is not supported.',
    })
  }
})

app.get('/translate', async (req, res) => {
  const text = req.query.text
  let lang = req.query?.lang

  if (!lang) lang = 'en'
  res.setHeader('Content-Type', 'text/event-stream')
  res.setHeader('Cache-Control', 'no-cache')
  res.setHeader('Connection', 'keep-alive')

  translateDoubao(text, lang, (r) => {
    if (r.finish_reason === 'stop') {
      res.write(`data: ${JSON.stringify(r)}\n\n`)
      res.end()
    } else {
      res.write(`data: ${JSON.stringify(r)}\n\n`)
    }
  })
})

app.post('/translate', async (req, res) => {
  const text = req.body.text
  let lang = req.body?.lang

  if (!lang) lang = 'en'
  res.setHeader('Content-Type', 'text/event-stream')
  res.setHeader('Cache-Control', 'no-cache')
  res.setHeader('Connection', 'keep-alive')

  translateDoubao(text, lang, (r) => {
    if (r.finish_reason === 'stop') {
      res.write(`data: ${JSON.stringify(r)}\n\n`)
      res.end()
    } else {
      res.write(`data: ${JSON.stringify(r)}\n\n`)
    }
  })
})

app.post('/image_recognize', async (req, res) => {
  const imageUrls = req.body.image_urls
  const question = req.body.question

  const ret ={
    ret: 0,
    message: 'success',
  }

  recognizeImages(imageUrls, question).then((r) => {
    ret.content = r
    res.json(ret)
  }).catch((err) => {
    ret.ret = -1
    ret.message = err.message
    res.json(ret)
  })
})


http.createServer(app).listen(9445)

let runGetTuyaDeviceList = () => {
  if (!isGettingList) {
    isGettingList = true
    tuyaIdList.forEach((id) => {
      let detail = {
        deviceList: [],
        sceneList: [],
        commandList: [],
      }
      getDeviceListDetail({ deviceId: id, isRefresh: true }).then(
        async (res) => {
          const dList = await checkDeviceType(res.deviceList)
          detail.deviceList = dList
          detail.commandList = res.commandList
          getAllSceneList({ deviceId: id, isRefresh: true }).then((res) => {
            detail.sceneList = res
            tuyaDeviceMap[id] = detail
            isGettingList = false
          })
        }
      )
    })
  }
}

runGetTuyaDeviceList()
let intv = setInterval(() => {
  runGetTuyaDeviceList()
}, 3000)

const wss = new WebSocket.Server({ port: PORT })
const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}
console.log(`WebSocket server is running on ws://localhost:${PORT}`)

function endsWithChinesePunctuation(str) {
  // 定义一个包含中文结束标点符号的数组
  const punctuationMarks = ['。', '？', '！']

  // 获取字符串的最后一个字符
  const lastChar = str.slice(-1)

  // 判断最后一个字符是否在标点符号数组中
  return punctuationMarks.includes(lastChar)
}

wss.on('connection', (ws) => {
  // End marker received, process the audio
  const appid = '8359578722'
  const token = 'Zol8wcj36NJGQC141tLZgNkvdVg-o_a7'
  const cluster = 'volcengine_streaming_common'
  const audioFormat = 'wav'
  let audioBuffer = Buffer.alloc(0)
  const asrClient = new AsrClient(appid, token, cluster)
  // asrClient.init()
  const reqList = []
  let isEnd = false
  let isFinished = false
  let reqCount = 0
  let resCount = 0
  let isReqing = false
  let isPassAsr = false
  let asrText = ''
  let lastAsrText = ''
  let isSameCount = 0
  let personList = []
  let deviceList = []
  let sceneList = []
  let commandList = ['开关']

  let handleRes = async (text) => {
    const checkCallNum = await preCheckCall(text)
    console.log(getTime(), 'preCheck', checkCallNum)
    if (checkCallNum.includes('1')) {
      // 问答
      chatDoubao(text, (resChat) => {
        ws.send(JSON.stringify(resChat))
      })
    } else if (checkCallNum.includes('2')) {
      // 文生图
      let process = 0
      const rJsonStart = {
        target_audio: '',
        source_text: text,
        target_text: '好的，正在为您生成图片。请稍等...\n',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'START',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'text2image',
        image_url: '',
      }
      ws.send(JSON.stringify(rJsonStart))
      let processInterval = setInterval(() => {
        process += 16
        const rJsonProcess = {
          target_audio: '',
          source_text: text,
          target_text: `${process > 99 ? 99 : process}%..${process > 75 ? '\n' : ''
            }`,
          target_audio_format: 'pcm',
          sample_rate: 22050,
          finish_reason: 'START',
          is_tuya: '0',
          tuya_ret: '0',
          type: 'text2sound',
          image_url: '',
        }
        ws.send(JSON.stringify(rJsonProcess))
      }, 2500)
      const imageRes = await textToImage(text)
      clearInterval(processInterval)
      const imageUrl = imageRes.url
      const target_text = '\n' + imageRes.text

      const rJson = {
        target_audio: '',
        source_text: text,
        target_text,
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: '',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'text2image',
        image_url: imageUrl,
      }
      ws.send(JSON.stringify(rJson))
      const rJsonStop = {
        target_audio: '',
        source_text: text,
        target_text: '',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'STOP',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'text2image',
      }
      ws.send(JSON.stringify(rJsonStop))
    } else if (checkCallNum.includes('3')) {
      // 呼叫
      const callRes = await getCall(text, personList)
      const person_in_list = JSON.parse(callRes).person_in_list
      console.log(getTime(), person_in_list)
      let tText = ''
      if (person_in_list) tText = `好的，开始呼叫${person_in_list}`
      else tText = `您呼叫的对象不在列表中`
      // 呼叫
      const rJson = {
        target_audio: '',
        source_text: text,
        target_text: `${tText}`,
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'START',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'call',
      }
      ws.send(JSON.stringify(rJson))
      const rJsonStop = {
        target_audio: '',
        source_text: text,
        target_text: '',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'STOP',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'call',
      }
      ws.send(JSON.stringify(rJsonStop))
    } else if (checkCallNum.includes('4')) {
      // 文生音乐
      const rJsonStart = {
        target_audio: '',
        source_text: text,
        target_text: '好的，正在为您生成音乐。请稍等...\n',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'START',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'text2sound',
        image_url: '',
      }
      ws.send(JSON.stringify(rJsonStart))
      textToSound(text).then((resSong) => {
        const Result = resSong.Result
        const { TaskID, PredictedWaitTime } = Result
        let process = 0
        let processInterval = setInterval(() => {
          process += 13
          const rJsonProcess = {
            target_audio: '',
            source_text: text,
            target_text: `${process > 99 ? 99 : process}%..${process > 75 ? '\n' : ''
              }`,
            target_audio_format: 'pcm',
            sample_rate: 22050,
            finish_reason: 'START',
            is_tuya: '0',
            tuya_ret: '0',
            type: 'text2sound',
            image_url: '',
          }
          ws.send(JSON.stringify(rJsonProcess))
        }, 2500)

        setTimeout(() => {
          let songInterval = setInterval(() => {
            console.log('get song status')
            getSongStatus(TaskID).then((resStatus) => {
              if (resStatus.Code === 0) {
                const status = resStatus.Result.Status
                if (status === 2) {
                  const song = resStatus.Result.SongDetail
                  clearInterval(songInterval)
                  clearInterval(processInterval)
                  songInterval = null
                  const rJsonSong = {
                    target_audio: song.AudioUrl,
                    source_text: text,
                    target_text: '\n' + song.Lyrics,
                    target_audio_format: 'pcm',
                    sample_rate: 22050,
                    finish_reason: 'START',
                    is_tuya: '0',
                    tuya_ret: '0',
                    type: 'text2sound',
                    image_url: '',
                  }
                  ws.send(JSON.stringify(rJsonSong))
                  const rJsonEnd = {
                    target_audio: '',
                    source_text: text,
                    target_text: '',
                    target_audio_format: 'pcm',
                    sample_rate: 22050,
                    finish_reason: 'STOP',
                    is_tuya: '0',
                    tuya_ret: '0',
                    type: 'text2sound',
                    image_url: '',
                  }
                  ws.send(JSON.stringify(rJsonEnd))
                }
              }
            })

            if (isFinished) clearInterval(songInterval)
          }, 1000)
        }, PredictedWaitTime * 1000)
      })
    } else if (checkCallNum.includes('5')) {
      // 智能家居控制
      const smartRes = await getSmartControl(
        deviceList,
        text,
        sceneList,
        commandList
      )
      const result = JSON.parse(smartRes.rMessageText)
      let rText = ''
      if (result.m) {
        rText = `好的，为您${result.s === 1 ? '打开' : '关闭'}${result.m}`
      } else {
        rText = '列表中不存在该设备'
      }
      const rJson = {
        target_audio: '',
        source_text: text,
        target_text: `${rText}`,
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'START',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'smartControl',
      }
      ws.send(JSON.stringify(rJson))
      const rJsonStop = {
        target_audio: '',
        source_text: text,
        target_text: '',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'STOP',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'smartControl',
      }
      ws.send(JSON.stringify(rJsonStop))
    } else if (checkCallNum.includes('6')) {
      // 图片识别
      const rJson = {
        target_audio: '',
        source_text: text,
        target_text: '好的，正在为您识别图片。请稍等...\n',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'START',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'imageRecognize',
        image_url: '',
      }
      ws.send(JSON.stringify(rJson))
      const rJsonStop = {
        target_audio: '',
        source_text: text,
        target_text: '',
        target_audio_format: 'pcm',
        sample_rate: 22050,
        finish_reason: 'STOP',
        is_tuya: '0',
        tuya_ret: '0',
        type: 'imageRecognize',
      }
      ws.send(JSON.stringify(rJsonStop))
    }
  }
  let interval = null
  setTimeout(() => {
    interval = setInterval(() => {
      if (isEnd) {
        asrClient.setEnd(reqCount)
      }

      if (reqList.length > 0) {
        if (!isReqing) {
          isReqing = true
          asrClient
            .requestAsr(reqList.shift(), (endRes) => { })
            .then(async (res) => {
              isReqing = false
              if (res) {
                let text = ''
                try {
                  text = res[res.length - 1].result[0].text
                  text = text.replace(/[\r\n。？！!?]/g, '')
                } catch (err) { }
                let isPass = false
                try {
                  const isEndsWithChinesePunctuation =
                    endsWithChinesePunctuation(text)
                  if (text === lastAsrText && text) {
                    isSameCount++
                  } else {
                    lastAsrText = text
                    isSameCount = 0
                  }

                  // if (endsWithChinesePunctuation(text)) {
                  //   if (isSameCount>=3 ) {
                  //     isPass= true
                  //   }
                  // }
                  if (isSameCount >= 2) {
                    isPass = true
                  }
                } catch (err) {
                  console.log(err)
                }

                resCount++
                if ((resCount === reqCount && isEnd) || isPass) {
                  clearInterval(interval)
                  asrClient.end()
                  const text = res[res.length - 1].result[0].text
                  const rJson = {
                    target_audio: '',
                    source_text: text,
                    target_text: '',
                    target_audio_format: 'pcm',
                    sample_rate: 22050,
                    finish_reason: 'START',
                    is_tuya: '0',
                    tuya_ret: '0',
                  }
                  ws.send(JSON.stringify(rJson))
                  handleRes(text)
                }
              }
            })
            .catch((err) => {
              isReqing = false
              ws.send(JSON.stringify({ status: 'error', message: err.message }))
              asrClient.end()
              clearInterval(interval)
            })
        }
      } else {
        if (isPassAsr) {
          clearInterval(interval)
          handleRes(asrText)
        }
      }
    }, 10)
  }, 1)

  ws.on('message', async (data) => {
    try {
      // Check if the message is JSON to detect the end marker
      const message = JSON.parse(data.toString())
      console.log(new Date().toISOString(), message)

      if (message.end === true) {
        // end
        isEnd = true
      } else if (message.asrText) {
        asrText = message.asrText
        isPassAsr = true
      } else if (message.personList) {
        personList = message.personList
        deviceList = message.lightList
      } else {
        try {
        } catch (error) {
          ws.send(JSON.stringify({ status: 'error', message: error.message }))
        }
      }
    } catch (error) {
      console.log(new Date().toISOString(), 'recieved binary data')
      // In case parsing fails, treat it as binary data
      reqList.push(data)
      reqCount++
    }
  })

  ws.on('close', () => {
    console.log('Client disconnected')
    isFinished = true
    clearInterval(interval)
    asrClient.end()
  })
})
