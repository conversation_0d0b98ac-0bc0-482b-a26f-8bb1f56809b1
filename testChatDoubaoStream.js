const { chat<PERSON><PERSON><PERSON> } = require('./chat_doubao');
const { sampleBase64 } = require('./imageBase64');

// 测试纯文本聊天（原有功能）
async function testTextChat() {
  console.log('=== 测试纯文本聊天 ===\n');
  
  return new Promise((resolve) => {
    let responses = [];
    
    chatDoubao(
      '你好，请简单介绍一下自己',
      (response) => {
        responses.push(response);
        console.log('收到响应:', {
          text: response.target_text,
          finish_reason: response.finish_reason,
          status: response.status || 'success'
        });
        
        if (response.finish_reason === 'STOP') {
          console.log('\n✅ 纯文本聊天测试完成');
          console.log(`📊 总共收到 ${responses.length} 个响应`);
          resolve();
        }
      },
      false, // isAudio
      []     // imageUrls - 空数组表示纯文本
    );
  });
}

// 测试图片+文本聊天（新功能）
async function testImageChat() {
  console.log('\n=== 测试图片+文本聊天 ===\n');
  
  return new Promise((resolve) => {
    let responses = [];
    
    chatDoubao(
      '请描述这张图片中的内容，简洁回答',
      (response) => {
        responses.push(response);
        console.log('收到响应:', {
          text: response.target_text,
          finish_reason: response.finish_reason,
          status: response.status || 'success'
        });
        
        if (response.finish_reason === 'STOP') {
          console.log('\n✅ 图片+文本聊天测试完成');
          console.log(`📊 总共收到 ${responses.length} 个响应`);
          resolve();
        }
      },
      false, // isAudio
      [sampleBase64] // imageUrls - 包含图片
    );
  });
}

// 测试多张图片聊天
async function testMultiImageChat() {
  console.log('\n=== 测试多张图片聊天 ===\n');
  
  return new Promise((resolve) => {
    let responses = [];
    
    chatDoubao(
      '请分析这些图片的共同特点',
      (response) => {
        responses.push(response);
        console.log('收到响应:', {
          text: response.target_text,
          finish_reason: response.finish_reason,
          status: response.status || 'success'
        });
        
        if (response.finish_reason === 'STOP') {
          console.log('\n✅ 多张图片聊天测试完成');
          console.log(`📊 总共收到 ${responses.length} 个响应`);
          resolve();
        }
      },
      false, // isAudio
      [sampleBase64, sampleBase64] // 多张相同图片用于测试
    );
  });
}

// 测试错误处理
async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===\n');
  
  return new Promise((resolve) => {
    let responses = [];
    
    chatDoubao(
      '测试消息',
      (response) => {
        responses.push(response);
        console.log('收到响应:', {
          text: response.target_text,
          finish_reason: response.finish_reason,
          status: response.status || 'success',
          error: response.message || null
        });
        
        if (response.finish_reason === 'STOP' || response.status === 'error') {
          console.log('\n✅ 错误处理测试完成');
          console.log(`📊 总共收到 ${responses.length} 个响应`);
          resolve();
        }
      },
      false, // isAudio
      ['invalid-image-url'] // 无效的图片URL
    );
  });
}

// 实时显示聊天内容的测试
async function testRealTimeDisplay() {
  console.log('\n=== 测试实时显示效果 ===\n');
  
  return new Promise((resolve) => {
    let fullText = '';
    let responses = [];
    
    console.log('🤖 AI回复: ');
    
    chatDoubao(
      '请用一段话介绍人工智能的发展历程',
      (response) => {
        responses.push(response);
        
        if (response.target_text && response.finish_reason !== 'STOP') {
          // 实时显示文本，模拟打字机效果
          process.stdout.write(response.target_text);
          fullText += response.target_text;
        }
        
        if (response.finish_reason === 'STOP') {
          console.log('\n\n✅ 实时显示测试完成');
          console.log(`📝 完整回复: "${fullText}"`);
          console.log(`📊 总共收到 ${responses.length} 个响应`);
          resolve();
        }
      },
      false, // isAudio
      []     // 纯文本
    );
  });
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始测试修改后的 chat_doubao.js\n');
  
  try {
    await testTextChat();
    await testImageChat();
    // await testMultiImageChat();
    // await testRealTimeDisplay();
    // await testErrorHandling(); // 可能会失败，所以放在最后或注释掉
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n📋 测试总结:');
    console.log('✅ 纯文本聊天 - 保持原有功能');
    console.log('✅ 图片+文本聊天 - 新增功能');
    console.log('✅ 多张图片聊天 - 新增功能');
    console.log('✅ 实时显示效果 - 流式输出');
    console.log('✅ 使用 axios 替代 OpenAI SDK');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 如果直接运行此文件，则执行所有测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testTextChat,
  testImageChat,
  testMultiImageChat,
  testErrorHandling,
  testRealTimeDisplay,
  runAllTests
};
