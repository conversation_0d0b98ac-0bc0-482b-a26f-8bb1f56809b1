# 图片识别流式输出功能

## 概述

`imageRecognize.js` 已经升级支持流式输出功能，可以实时接收AI模型的识别结果，而不需要等待完整响应。

## 功能特性

- ✅ **流式输出**: 实时接收AI识别结果，提升用户体验
- ✅ **向后兼容**: 保留原有的非流式接口
- ✅ **错误处理**: 完善的错误处理和回调机制
- ✅ **灵活回调**: 支持数据接收、完成和错误三种回调

## API 接口

### 1. 流式识别 - `recognizeImagesStream`

```javascript
const { recognizeImagesStream } = require('./imageRecognize');

await recognizeImagesStream(
  imageUrls,           // 图片URL数组
  question,            // 提问内容（可选）
  onData,              // 数据回调函数（可选）
  onComplete,          // 完成回调函数（可选）
  onError              // 错误回调函数（可选）
);
```

#### 回调函数说明

- **onData(chunk, fullContent)**: 每次接收到新数据时调用
  - `chunk`: 当前接收到的文本片段
  - `fullContent`: 到目前为止的完整内容

- **onComplete(fullContent)**: 识别完成时调用
  - `fullContent`: 完整的识别结果

- **onError(error)**: 出现错误时调用
  - `error`: 错误对象

### 2. 非流式识别 - `recognizeImages` (向后兼容)

```javascript
const { recognizeImages } = require('./imageRecognize');

const result = await recognizeImages(imageUrls, question);
```

## 使用示例

### 基础流式使用

```javascript
const { recognizeImagesStream } = require('./imageRecognize');
const { sampleBase64 } = require('./imageBase64');

async function basicStreamExample() {
  const imageUrls = [sampleBase64];
  
  const result = await recognizeImagesStream(
    imageUrls,
    "识别这张图片中的内容",
    // 实时接收数据
    (chunk, fullContent) => {
      process.stdout.write(chunk); // 实时显示
    },
    // 完成时调用
    (fullContent) => {
      console.log('\n识别完成！');
    }
  );
}
```

### 高级流式使用

```javascript
async function advancedStreamExample() {
  let startTime = Date.now();
  let firstChunkTime = null;
  
  const result = await recognizeImagesStream(
    [sampleBase64],
    "详细描述这张图片",
    // onData - 数据回调
    (chunk, fullContent) => {
      if (firstChunkTime === null) {
        firstChunkTime = Date.now();
        console.log(`首个响应时间: ${firstChunkTime - startTime}ms`);
      }
      
      // 实时更新UI或处理数据
      updateUI(chunk);
    },
    // onComplete - 完成回调
    (fullContent) => {
      const totalTime = Date.now() - startTime;
      console.log(`总耗时: ${totalTime}ms`);
      console.log(`完整结果: ${fullContent}`);
    },
    // onError - 错误回调
    (error) => {
      console.error('识别失败:', error.message);
    }
  );
}
```

## 测试

运行测试文件来体验流式功能：

```bash
node testStreamRecognition.js
```

测试包含：
1. 基础流式识别测试
2. 流式 vs 非流式性能对比

## 优势

### 流式输出的优势：
- **更快的首字节时间**: 用户可以立即看到开始的识别结果
- **更好的用户体验**: 实时反馈，避免长时间等待
- **更灵活的处理**: 可以在接收过程中进行实时处理

### 使用场景：
- 儿童拍照玩具的实时显示
- 需要快速反馈的交互应用
- 长文本识别结果的逐步显示

## 注意事项

1. **网络稳定性**: 流式输出对网络稳定性要求较高
2. **错误处理**: 建议实现完善的错误回调处理
3. **资源管理**: 流式连接会保持较长时间，注意资源释放

## 兼容性

- 保持与原有 `recognizeImages` 函数的完全兼容
- 新增的流式功能不影响现有代码
- 支持 Node.js 环境下的流式处理
