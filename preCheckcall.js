const OpenAI = require('openai')

let isopenAI = false
let client
let model = isopenAI ? 'gpt-4o-mini' : 'ep-20241126141918-cfstz'
if (isopenAI) {
  client = new OpenAI({
    apiKey: '***************************************************',
  })
} else {
  client = new OpenAI({
    apiKey: '3c4635c7-281b-4a28-951d-0b674d7ad600',
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  })
}

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

async function preCheckCall(message){
  return new Promise(async resolve => {
    try {
      const completion = await client.chat.completions.create(
        {
          model,
          messages: [
            {
              role: 'system',
              content:
                `
                -你是一个智能语音助手，用户将输入由语音识别而来的文本内容。请你根据内容来判断用户的意图：1.普通的问答2.文生图指令3.呼叫指令4.文生音乐指令5.智能家居控制指令6.图片识别指令
                -只返回1/2/3/4/5/6 。只返回数字结果，不要返回除数字结果以外的任何内容`,
            },
            {
              role: 'user',
              content: message,
            },
          ],
        },
      )
      
      const res = completion.choices[0].message.content

      let rText = res
      if (res.includes('问答')) rText = '1'
      else if (res.includes('文生图')) rText = '2'
      else if (res.includes('呼叫')) rText = '3'
      else if (res.includes('文生音乐')) rText = '4'
      else if (res.includes('智能家居')) rText = '5'
      if (rText !== '1' && rText !== '2' && rText !== '3' && rText !== '4' && rText !== '5') rText = '1'
      resolve(res)
    }catch(err) {
      resolve(err)
    }
  })


}

module.exports = { preCheckCall }